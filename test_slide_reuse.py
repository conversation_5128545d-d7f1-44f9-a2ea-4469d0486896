#!/usr/bin/env python3
"""
Test script để kiểm tra slide generation với khả năng tái sử dụng template
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.slide_generation_service import get_slide_generation_service

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_slide_reuse():
    """Test slide generation với khả năng tái sử dụng template"""
    
    try:
        logger.info("🚀 Starting slide reuse test...")
        
        # Get service
        slide_service = get_slide_generation_service()
        
        if not slide_service.is_available():
            logger.error("❌ Slide generation service not available")
            return
        
        # Test parameters
        lesson_id = "test_lesson_001"
        template_id = "1example_template_id"  # Replace with actual template ID
        
        logger.info(f"📚 Testing with lesson_id: {lesson_id}")
        logger.info(f"📄 Testing with template_id: {template_id}")
        
        # Generate slides
        result = await slide_service.generate_slides_from_lesson(
            lesson_id=lesson_id,
            template_id=template_id,
            presentation_title="Test Slide Reuse - Bài học mẫu"
        )
        
        if result["success"]:
            logger.info("✅ Slide generation completed successfully!")
            logger.info(f"📊 Result: {result}")
            
            # Check if slides were created/updated
            slides_created = result.get("slides_created", 0)
            presentation_id = result.get("presentation_id")
            web_view_link = result.get("web_view_link")
            
            logger.info(f"🎯 Slides created/updated: {slides_created}")
            logger.info(f"📄 Presentation ID: {presentation_id}")
            logger.info(f"🔗 Web view link: {web_view_link}")
            
        else:
            logger.error(f"❌ Slide generation failed: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

async def test_slide_mapping_logic():
    """Test slide mapping logic riêng biệt"""
    
    try:
        logger.info("🧪 Testing slide mapping logic...")
        
        # Mock data để test
        mock_parsed_content = {
            "parsed_data": {
                "LessonName": [{"content": "Bài 1: Cấu hình electron"}],
                "LessonDescription": [{"content": "Bài học về cấu hình electron trong nguyên tử"}],
                "CreatedDate": [{"content": "12-07-2025"}],
                "TitleName": [
                    {"content": "I. Khái niệm cấu hình electron"},
                    {"content": "II. Các quy tắc sắp xếp electron"}
                ],
                "TitleContent": [
                    {"content": "Cấu hình electron là cách sắp xếp các electron trong orbital"},
                    {"content": "Có 3 quy tắc chính để sắp xếp electron"}
                ]
            },
            "slide_summaries": [
                {
                    "slide_number": 1,
                    "placeholders": ["LessonName", "LessonDescription", "CreatedDate"],
                    "placeholder_counts": {"LessonName": 1, "LessonDescription": 1, "CreatedDate": 1}
                },
                {
                    "slide_number": 2,
                    "placeholders": ["TitleName", "TitleContent"],
                    "placeholder_counts": {"TitleName": 1, "TitleContent": 1}
                },
                {
                    "slide_number": 3,
                    "placeholders": ["TitleName", "TitleContent"],
                    "placeholder_counts": {"TitleName": 1, "TitleContent": 1}
                }
            ]
        }
        
        mock_template = {
            "slides": [
                {
                    "slideId": "slide_intro",
                    "elements": [
                        {"objectId": "obj1", "Type": "LessonName", "max_length": 100},
                        {"objectId": "obj2", "Type": "LessonDescription", "max_length": 200},
                        {"objectId": "obj3", "Type": "CreatedDate", "max_length": 50}
                    ]
                },
                {
                    "slideId": "slide_content",
                    "elements": [
                        {"objectId": "obj4", "Type": "TitleName", "max_length": 150},
                        {"objectId": "obj5", "Type": "TitleContent", "max_length": 500}
                    ]
                }
            ]
        }
        
        # Get service và test mapping
        slide_service = get_slide_generation_service()
        slide_service._ensure_service_initialized()
        
        # Test mapping function
        mapping_result = await slide_service._map_parsed_content_to_slides(
            mock_parsed_content,
            mock_template
        )
        
        if mapping_result["success"]:
            logger.info("✅ Slide mapping test successful!")
            logger.info(f"📊 Mapped slides: {len(mapping_result['slides'])}")
            
            for i, slide in enumerate(mapping_result["slides"]):
                logger.info(f"🎯 Slide {i+1}: {slide['slideId']} - {len(slide.get('elements', []))} elements")
                
        else:
            logger.error(f"❌ Slide mapping test failed: {mapping_result.get('error')}")
            
    except Exception as e:
        logger.error(f"❌ Mapping test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_slide_filtering():
    """Test slide filtering logic"""

    try:
        logger.info("🧪 Testing slide filtering logic...")

        # Mock mapped slides data
        mock_mapped_slides = [
            {
                "slideId": "slide_intro",
                "original_template_id": "slide_intro",
                "action": "update",
                "elements": [
                    {"objectId": "obj1", "text": "Bài 1: Cấu hình electron"},
                    {"objectId": "obj2", "text": "Bài học về cấu hình electron"},
                    {"objectId": "obj3", "text": "12-07-2025"}
                ]
            },
            {
                "slideId": "slide_content",
                "original_template_id": "slide_content",
                "action": "update",
                "elements": [
                    {"objectId": "obj4", "text": "I. Khái niệm cấu hình electron"},
                    {"objectId": "obj5", "text": "Cấu hình electron là cách sắp xếp..."}
                ]
            },
            {
                "slideId": "slide_content",  # Same template reused
                "original_template_id": "slide_content",
                "action": "update",
                "elements": [
                    {"objectId": "obj4", "text": "II. Các quy tắc sắp xếp electron"},
                    {"objectId": "obj5", "text": "Có 3 quy tắc chính..."}
                ]
            }
        ]

        # Get service và test filtering
        slide_service = get_slide_generation_service()
        slide_service._ensure_service_initialized()

        # Test filtering function
        filtered_slides = slide_service._filter_used_slides(mock_mapped_slides)

        logger.info("✅ Slide filtering test successful!")
        logger.info(f"📊 Input slides: {len(mock_mapped_slides)}")
        logger.info(f"📊 Filtered slides: {len(filtered_slides)}")

        for i, slide in enumerate(filtered_slides):
            slide_id = slide.get("slideId")
            action = slide.get("action")
            updates_count = len(slide.get("updates", {}))
            logger.info(f"🎯 Slide {i+1}: {slide_id} - {action} - {updates_count} updates")

        # Check if template is reused
        slide_ids = [s.get("slideId") for s in filtered_slides]
        unique_slide_ids = set(slide_ids)
        logger.info(f"📈 Template reuse: {len(slide_ids)} total slides, {len(unique_slide_ids)} unique templates")

        if len(slide_ids) > len(unique_slide_ids):
            logger.info("✅ Template reuse working correctly!")
        else:
            logger.warning("⚠️ No template reuse detected")

    except Exception as e:
        logger.error(f"❌ Filtering test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Slide Reuse Test Script")
    print("=" * 50)

    # Run tests
    asyncio.run(test_slide_mapping_logic())
    print("\n" + "=" * 50)
    asyncio.run(test_slide_filtering())
    print("\n" + "=" * 50)

    # Uncomment to test full workflow (requires valid lesson_id and template_id)
    # asyncio.run(test_slide_reuse())
