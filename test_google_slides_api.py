#!/usr/bin/env python3
"""
Test script để kiểm tra Google Slides API operations
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.google_slides_service import get_google_slides_service

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_google_slides_operations():
    """Test Google Slides API operations"""
    
    try:
        logger.info("🚀 Starting Google Slides API test...")
        
        # Get service
        slides_service = get_google_slides_service()
        
        if not slides_service.is_available():
            logger.error("❌ Google Slides service not available")
            return
        
        # Test parameters - REPLACE WITH ACTUAL IDs
        test_presentation_id = "1example_presentation_id"  # Replace with actual presentation ID
        
        logger.info(f"📄 Testing with presentation_id: {test_presentation_id}")
        
        # Test 1: Update presentation content với create_from_template
        test_slides_content = [
            {
                "slideId": "content_slide_test_001",
                "template_slide_id": "slide_template_001",  # Replace with actual template slide ID
                "action": "create_from_template",
                "updates": {
                    "obj1": "Test Title Content",
                    "obj2": "Test Description Content"
                }
            },
            {
                "slideId": "content_slide_test_002", 
                "template_slide_id": "slide_template_002",  # Replace with actual template slide ID
                "action": "create_from_template",
                "updates": {
                    "obj3": "Test Content 1",
                    "obj4": "Test Content 2"
                }
            }
        ]
        
        logger.info("🔄 Testing update_copied_presentation_content...")
        update_result = await slides_service.update_copied_presentation_content(
            test_presentation_id,
            test_slides_content
        )
        
        if update_result["success"]:
            logger.info("✅ Update presentation content successful!")
            logger.info(f"📊 Result: {update_result}")
            
            slides_created = update_result.get("slides_created", 0)
            slides_updated = update_result.get("slides_updated", 0)
            
            logger.info(f"🎯 Slides created: {slides_created}")
            logger.info(f"🔄 Slides updated: {slides_updated}")
            
            # Test 2: Delete template slides
            template_slide_ids = ["slide_template_001", "slide_template_002"]  # Replace with actual IDs
            
            logger.info("🗑️ Testing delete_template_slides...")
            delete_result = await slides_service.delete_template_slides(
                test_presentation_id,
                template_slide_ids
            )
            
            if delete_result["success"]:
                logger.info("✅ Delete template slides successful!")
                logger.info(f"📊 Delete result: {delete_result}")
                
                slides_deleted = delete_result.get("slides_deleted", 0)
                logger.info(f"🗑️ Template slides deleted: {slides_deleted}")
            else:
                logger.error(f"❌ Delete template slides failed: {delete_result.get('error')}")
            
        else:
            logger.error(f"❌ Update presentation content failed: {update_result.get('error')}")
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

async def test_slide_duplication_logic():
    """Test slide duplication logic riêng biệt"""
    
    try:
        logger.info("🧪 Testing slide duplication logic...")
        
        # Mock test để kiểm tra logic
        mock_slide_request = {
            "slideId": "new_slide_123",
            "template_slide_id": "template_slide_456",
            "action": "create_from_template",
            "updates": {
                "text_obj_1": "New slide title",
                "text_obj_2": "New slide content"
            }
        }
        
        logger.info(f"📋 Mock slide request: {mock_slide_request}")
        
        # Kiểm tra logic tạo duplicate request
        template_slide_id = mock_slide_request.get("template_slide_id")
        new_slide_id = mock_slide_request.get("slideId")
        
        duplicate_request = {
            'duplicateObject': {
                'objectId': template_slide_id,
                'objectIds': {
                    template_slide_id: new_slide_id
                }
            }
        }
        
        logger.info(f"✅ Generated duplicate request: {duplicate_request}")
        
        # Kiểm tra logic update content
        updates = mock_slide_request.get("updates", {})
        update_requests = []
        
        for object_id, text_content in updates.items():
            update_request = {
                "replaceAllText": {
                    "containsText": {
                        "text": "",  # Replace all text
                        "matchCase": False
                    },
                    "replaceText": text_content,
                    "pageObjectIds": [new_slide_id]
                }
            }
            update_requests.append(update_request)
        
        logger.info(f"✅ Generated update requests: {len(update_requests)} requests")
        for i, req in enumerate(update_requests):
            logger.info(f"   Update {i+1}: {req}")
            
    except Exception as e:
        logger.error(f"❌ Duplication logic test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Google Slides API Test Script")
    print("=" * 50)
    
    # Run logic test first
    asyncio.run(test_slide_duplication_logic())
    print("\n" + "=" * 50)
    
    # Uncomment to test actual API (requires valid presentation_id and template slide IDs)
    # asyncio.run(test_google_slides_operations())
